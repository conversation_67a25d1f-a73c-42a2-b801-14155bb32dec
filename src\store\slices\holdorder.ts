import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { CartItem } from './cartSlice';

// Generate random order number: ORD + 10 random alphanumeric characters
const generateOrderNumber = (): string => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = 'ORD';
    for (let i = 0; i < 10; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
};

interface SelectedCoupon {
    _id: string;
    series: string;
    description: string;
    discount: number;
    discountType: string;
}

export interface HeldOrder {
    // Order specific fields
    orderNo: string;
    tableNo: string;
    operatorName: string;
    customer: string;
    customerId?: string;
    amount: number;
    recordDate: string;

    // Cart state fields
    items: CartItem[];
    discountAmount: string;
    discountReason: string;
    discountType: string;
    taxPercentage: number;
    tipAmount: string;
    loyaltyPercentage: number;
    loyaltyFixedAmount: string;
    loyaltyType: string;
    invoiceNumber: string;
    selectedCoupon: SelectedCoupon | null;
    couponOfferAmount: number;

    // Additional metadata
    isRefundMode: boolean;
    selectAllForRefund: boolean;
}

interface HoldOrderState {
    heldOrders: HeldOrder[];
    isLoading: boolean;
    error: string | null;
}

const initialState: HoldOrderState = {
    heldOrders: [],
    isLoading: false,
    error: null,
};

const holdOrderSlice = createSlice({
    name: 'holdOrder',
    initialState,
    reducers: {
        addHeldOrder: (state, action: PayloadAction<{
            cartState: {
                items: CartItem[];
                discountAmount: string;
                discountReason: string;
                discountType: string;
                taxPercentage: number;
                tipAmount: string;
                loyaltyPercentage: number;
                loyaltyFixedAmount: string;
                loyaltyType: string;
                invoiceNumber: string;
                selectedCoupon: SelectedCoupon | null;
                couponOfferAmount: number;
                isRefundMode: boolean;
                selectAllForRefund: boolean;
            };
            orderInfo: {
                tableNo: string;
                operatorName: string;
                customer: string;
                customerId?: string ;
                amount: number;
            };
        }>) => {
            const newHeldOrder: HeldOrder = {
                // Generate order number and record date
                orderNo: generateOrderNumber(),
                recordDate: new Date().toISOString(),

                // Order info from payload
                tableNo: action.payload.orderInfo.tableNo,
                operatorName: action.payload.orderInfo.operatorName,
                customer: action.payload.orderInfo.customer,
                customerId: action.payload.orderInfo.customerId,
                amount: action.payload.orderInfo.amount,

                // Cart state from payload
                items: action.payload.cartState.items,
                discountAmount: action.payload.cartState.discountAmount,
                discountReason: action.payload.cartState.discountReason,
                discountType: action.payload.cartState.discountType,
                taxPercentage: action.payload.cartState.taxPercentage,
                tipAmount: action.payload.cartState.tipAmount,
                loyaltyPercentage: action.payload.cartState.loyaltyPercentage,
                loyaltyFixedAmount: action.payload.cartState.loyaltyFixedAmount,
                loyaltyType: action.payload.cartState.loyaltyType,
                invoiceNumber: action.payload.cartState.invoiceNumber,
                selectedCoupon: action.payload.cartState.selectedCoupon,
                couponOfferAmount: action.payload.cartState.couponOfferAmount,
                isRefundMode: action.payload.cartState.isRefundMode,
                selectAllForRefund: action.payload.cartState.selectAllForRefund,
            };

            state.heldOrders.push(newHeldOrder);
        },

        removeHeldOrder: (state, action: PayloadAction<string>) => {
            // Remove held order by orderNo
            state.heldOrders = state.heldOrders.filter(order => order.orderNo !== action.payload);
        },

        updateHeldOrder: (state, action: PayloadAction<{
            orderNo: string;
            updates: Partial<HeldOrder>;
        }>) => {
            const orderIndex = state.heldOrders.findIndex(
                order => order.orderNo === action.payload.orderNo
            );

            if (orderIndex !== -1) {
                state.heldOrders[orderIndex] = {
                    ...state.heldOrders[orderIndex],
                    ...action.payload.updates,
                };
            }
        },

        clearAllHeldOrders: (state) => {
            state.heldOrders = [];
        },

        setHeldOrders: (state, action: PayloadAction<HeldOrder[]>) => {
            state.heldOrders = action.payload;
        },

        setLoading: (state, action: PayloadAction<boolean>) => {
            state.isLoading = action.payload;
        },

        setError: (state, action: PayloadAction<string | null>) => {
            state.error = action.payload;
        },
    },
});

export const {
    addHeldOrder,
    removeHeldOrder,
    updateHeldOrder,
    clearAllHeldOrders,
    setHeldOrders,
    setLoading,
    setError,
} = holdOrderSlice.actions;

// Selectors
export const selectHeldOrders = (state: RootState) => state.holdOrder.heldOrders;
export const selectHeldOrdersCount = (state: RootState) => state.holdOrder.heldOrders.length;
export const selectHeldOrderByOrderNo = (orderNo: string) => (state: RootState) =>
    state.holdOrder.heldOrders.find(order => order.orderNo === orderNo);
export const selectHeldOrdersByTable = (tableNo: string) => (state: RootState) =>
    state.holdOrder.heldOrders.filter(order => order.tableNo === tableNo);
export const selectHeldOrdersByCustomer = (customerId: string) => (state: RootState) =>
    state.holdOrder.heldOrders.filter(order => order.customerId === customerId);
export const selectHeldOrdersByOperator = (operatorName: string) => (state: RootState) =>
    state.holdOrder.heldOrders.filter(order => order.operatorName === operatorName);
export const selectHeldOrdersLoading = (state: RootState) => state.holdOrder.isLoading;
export const selectHeldOrdersError = (state: RootState) => state.holdOrder.error;

// Selector to get total amount of all held orders
export const selectTotalHeldOrdersAmount = (state: RootState) =>
    state.holdOrder.heldOrders.reduce((total, order) => total + order.amount, 0);

// Selector to get held orders sorted by date (newest first)
export const selectHeldOrdersSortedByDate = (state: RootState) =>
    [...state.holdOrder.heldOrders].sort((a, b) =>
        new Date(b.recordDate).getTime() - new Date(a.recordDate).getTime()
    );

export default holdOrderSlice.reducer;